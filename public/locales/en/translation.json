{"accordion": "Accordion", "account": "Account", "action": "Action", "activity": "Activity", "addNewUser": "Add new user", "editUser": "Edit user", "email": "Email", "invalid_email": "Please enter a valid email", "password": "Password", "repeat_password": "Repeat password", "change_password": "Change password", "fieldRequired": "This field is required", "firstNameRequired": "First name is required", "lastNameRequired": "Last name is required", "emailRequired": "Email is required", "invalidEmailFormat": "Enter a valid email address", "passwordRequired": "Password is required", "passwordMinLength": "Password must be at least 5 characters long", "passwordsDoNotMatch": "Passwords do not match", "privilegesRequired": "Privileges are required", "privileges": "Privileges", "USER_CREATE": "Create users", "USER_READ": "Read users", "USER_UPDATE": "Update users", "USER_DELETE": "Delete users", "EMPLOYEE_CREATE": "Create employees", "EMPLOYEE_READ": "Read employees", "EMPLOYEE_UPDATE": "Update employees", "EMPLOYEE_DELETE": "Delete employees", "BRANCH_CREATE": "Create branches", "BRANCH_READ": "Read branches", "BRANCH_UPDATE": "Update branches", "BRANCH_DELETE": "Delete branches", "CUSTOMER_CREATE": "Create customers", "CUSTOMER_READ": "Read customers", "CUSTOMER_UPDATE": "Update customers", "CUSTOMER_DELETE": "Delete customers", "selectAll": "Select all", "anotherAction": "Another action", "apps": "Apps", "base": "Base", "bounceRate": "Bounce Rate", "buttons": "Buttons", "calendar": "Calendar", "charts": "Charts", "colors": "Colors", "comments": "Comments", "components": "Components", "contacts": "Contacts", "country": "Country", "conversionRate": "Conversion Rate", "cpuUsage": "CPU Usage", "cpuUsageDescription": "{{number_of_processes}} processes. {{number_of_cores}} cores", "customers": "Customers", "dark": "Dark", "dashboard": "Dashboard", "configuration": "Configuration", "settings": "Settings", "profile": "Profile", "date": "{{date, datetime}}", "dateOnlyMonthName": "{{date, datetime(month: 'long')}}", "dateShortMonthName": "{{date, datetime(year: 'numeric'; month: 'short'; day: 'numeric' )}}", "day": "Day", "delete": "Delete", "disabledAction": "Disabled Action", "docs": "Docs", "error404": "Error 404", "error500": "Error 500", "edit": "Edit", "events": "Events", "extras": "Extras", "feeds": "Feeds", "female": "Female", "followers": "Followers", "forms": "Forms", "free": "Free", "friends": "Friends", "home": "Home", "icons": "Icons", "income": "Income", "info": "Info", "lastLogin": "Last login", "lastWeek": "Last week", "layout": "Layout", "light": "Light", "lockAccount": "Lock Account", "login": "<PERSON><PERSON>", "logout": "Logout", "male": "Male", "memoryUsage": "Memory Usage", "messages": "Messages", "messagesCounter": "You have {{counter}} messages", "meetings": "Meetings", "month": "Month", "new": "New", "newClient": "New client", "newClients": "New Clients", "newUserRegistered": "New user registered", "newUsers": "New Users", "notifications": "Notifications", "notificationsCounter": "You have {{counter}} notifications", "orders": "Orders", "organic": "Organic", "organicSearch": "Organic Search", "pages": "Pages", "pageviews": "Pageviews", "paymentMethod": "Payment Method", "payments": "Payments", "plugins": "Plugins", "projects": "Projects", "recurring": "Reccuring", "recurringClients": "Recurring Clients", "register": "Register", "registered": "Registered: ", "relativeTime": "{{val, relativetime}}", "registeredUsersCounter": "{{ counter }} registered users", "sale": "Sale", "salesReportIsReady": "Sales report is ready", "search": "Search...", "server": "Server", "serverOverloaded": "Server overloaded", "sessions": "Sessions", "somethingElseHere": "Something else here", "ssdUsage": "SSD Usage", "systemUtilization": "System Utilization", "taskCounter": "You have {{counter}} pending tasks", "tasks": "Tasks", "theme": "Theme", "today": "Today", "tomorrow": "Tomorrow", "traffic": "Traffic", "trafficAndSales": "Traffic & Sales", "tweets": "Tweets", "typography": "Typography", "unique": "Unique", "updates": "Updates", "usage": "Usage", "userDeleted": "User deleted", "user": "User", "users": "Users", "employees": "Employees", "addNewEmployee": "Add new employee", "editEmployee": "Edit employee", "firstName": "First name", "lastName": "Last name", "phoneNumber": "Phone number", "createSystemAccess": "Create system access", "subscription": "Subscription", "plan_name": "Plan:", "plan_status": "Status:", "plan_start_date": "Join date:", "plan_trial_expiration_date": "Trial until:", "plan_next_due_date": "Next due date:", "subscription_invoices": "Invoices", "subscription_no_pending_invoices": "No pending payments", "invoice_details": "Invoice details", "invoice_issue_date": "Issue date", "invoice_due_date": "Due date", "invoice_status": "Status", "invoice_total": "Total", "item_name": "Description", "item_from": "From", "item_to": "To", "item_total": "Total", "PAYMENT_COMPLETE": "Paid", "PAYMENT_PENDING": "Pending", "usersCounter": "{{counter}} Users", "validation": "Validation", "viewAllMessages": "View all messages", "viewAllTasks": "View all tasks", "viewsCounter": "{{counter}} Views", "visits": "Visits", "widgets": "Widgets", "saveChanges": "Save changes", "close": "Close", "year": "Year", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday", "january": "January", "february": "February", "march": "March", "april": "April", "may": "May", "june": "June", "july": "July", "august": "August", "september": "September", "october": "October", "november": "November", "december": "December", "itemsPerPage": "items per page", "role": "Role", "createDate": "Create date", "dragDropProfilePicture": "Drag and drop a profile picture here, or click to select", "fileTooLarge": "File is too large (max 5MB)", "invalidFileType": "Only image files are allowed", "maxFilesExceeded": "Only one file is allowed"}